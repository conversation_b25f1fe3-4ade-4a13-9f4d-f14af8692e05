#!/usr/bin/env python3
"""
Environment Verification Script for Ryu Enhanced SDN Middleware
===============================================================

This script verifies that all dependencies and components are properly
installed and configured before running the comprehensive test suite.

Usage:
    python3 verify_environment.py
"""

import os
import sys
import subprocess
import importlib.util

class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'

def log(message, color=Colors.BLUE):
    print(f"{color}[INFO]{Colors.END} {message}")

def success(message):
    print(f"{Colors.GREEN}[✓]{Colors.END} {message}")

def error(message):
    print(f"{Colors.RED}[✗]{Colors.END} {message}")

def warning(message):
    print(f"{Colors.YELLOW}[⚠]{Colors.END} {message}")

def run_command(command):
    """Run a command and return success status and output"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def check_python_version():
    """Check Python version"""
    log("Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        success(f"Python {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        error(f"Python {version.major}.{version.minor}.{version.micro} - requires Python 3.8+")
        return False

def check_virtual_environment():
    """Check if running in virtual environment"""
    log("Checking virtual environment...")
    venv = os.environ.get('VIRTUAL_ENV')
    if venv:
        success(f"Virtual environment: {venv}")
        return True
    else:
        warning("Not running in virtual environment")
        return False

def check_python_packages():
    """Check required Python packages"""
    log("Checking Python packages...")
    
    required_packages = {
        'ryu': 'Ryu SDN Framework',
        'pydantic': 'Data validation library',
        'yaml': 'YAML parser (pyyaml)',
        'requests': 'HTTP library',
        'scapy': 'Packet manipulation',
        'psutil': 'System utilities',
        'websockets': 'WebSocket support'
    }
    
    all_good = True
    for package, description in required_packages.items():
        try:
            if package == 'yaml':
                import yaml
            else:
                importlib.import_module(package)
            success(f"{package}: {description}")
        except ImportError:
            error(f"{package}: {description} - NOT FOUND")
            all_good = False
    
    return all_good

def check_system_commands():
    """Check required system commands"""
    log("Checking system commands...")
    
    commands = {
        'mn': 'Mininet network emulator',
        'ovs-vsctl': 'Open vSwitch control utility',
        'ryu-manager': 'Ryu controller manager',
        'python3': 'Python 3 interpreter'
    }
    
    all_good = True
    for cmd, description in commands.items():
        success_status, stdout, stderr = run_command(f"which {cmd}")
        if success_status:
            success(f"{cmd}: {description} - {stdout}")
        else:
            # Special case for ryu-manager - might be in venv
            if cmd == 'ryu-manager':
                venv = os.environ.get('VIRTUAL_ENV')
                if venv and os.path.exists(f"{venv}/bin/ryu-manager"):
                    success(f"{cmd}: {description} - {venv}/bin/ryu-manager")
                    continue
            error(f"{cmd}: {description} - NOT FOUND")
            all_good = False
    
    return all_good

def check_ryu_installation():
    """Check Ryu installation and middleware components"""
    log("Checking Ryu installation...")
    
    try:
        import ryu
        success(f"Ryu framework imported successfully")
        
        # Check if middleware components are available
        try:
            from ryu.app.middleware import MiddlewareAPI
            success("Middleware components available")
        except ImportError as e:
            error(f"Middleware components not found: {e}")
            return False
        
        # Check ryu-manager command
        success_status, stdout, stderr = run_command("ryu-manager --version")
        if success_status:
            success(f"ryu-manager: {stdout}")
        else:
            # Try from virtual environment
            venv = os.environ.get('VIRTUAL_ENV')
            if venv:
                success_status, stdout, stderr = run_command(f"{venv}/bin/ryu-manager --version")
                if success_status:
                    success(f"ryu-manager (venv): {stdout}")
                else:
                    error("ryu-manager command not working")
                    return False
            else:
                error("ryu-manager command not found")
                return False
        
        return True
        
    except ImportError as e:
        error(f"Ryu framework not found: {e}")
        return False

def check_network_tools():
    """Check network tools availability"""
    log("Checking network tools...")
    
    tools = {
        'ping': 'Network connectivity testing',
        'ip': 'Network interface management',
        'netstat': 'Network statistics (optional)',
        'ss': 'Socket statistics (alternative to netstat)'
    }
    
    available_tools = 0
    for tool, description in tools.items():
        success_status, stdout, stderr = run_command(f"which {tool}")
        if success_status:
            success(f"{tool}: {description}")
            available_tools += 1
        else:
            if tool in ['netstat', 'ss']:  # Optional tools
                warning(f"{tool}: {description} - not found (optional)")
            else:
                error(f"{tool}: {description} - NOT FOUND")
    
    return available_tools >= 2  # At least ping and ip should be available

def check_permissions():
    """Check if we can run network commands"""
    log("Checking permissions...")
    
    # Check if we can run sudo commands (needed for Mininet)
    success_status, stdout, stderr = run_command("sudo -n true")
    if success_status:
        success("Sudo access available")
        return True
    else:
        warning("Sudo access not available - you'll need to run tests with sudo")
        return False

def provide_recommendations():
    """Provide recommendations based on checks"""
    print(f"\n{Colors.BOLD}{Colors.BLUE}=== RECOMMENDATIONS ==={Colors.END}")
    
    venv = os.environ.get('VIRTUAL_ENV')
    if not venv:
        print("1. Activate your virtual environment:")
        print("   source venv/bin/activate")
        print()
    
    print("2. Install missing Python packages:")
    print("   pip install pydantic pyyaml requests scapy psutil websockets")
    print()
    
    print("3. Install missing system packages (Ubuntu/Debian):")
    print("   sudo apt install mininet openvswitch-switch curl")
    print()
    
    print("4. Run tests with proper permissions:")
    print("   sudo ./run_tests_venv.sh")
    print("   # or")
    print("   sudo -E python3 test_full_deployment.py")
    print()

def main():
    """Main verification function"""
    print(f"{Colors.BOLD}{Colors.BLUE}=== Ryu Enhanced Environment Verification ==={Colors.END}")
    print()
    
    checks = [
        ("Python Version", check_python_version),
        ("Virtual Environment", check_virtual_environment),
        ("Python Packages", check_python_packages),
        ("System Commands", check_system_commands),
        ("Ryu Installation", check_ryu_installation),
        ("Network Tools", check_network_tools),
        ("Permissions", check_permissions)
    ]
    
    results = {}
    for check_name, check_func in checks:
        print(f"\n{Colors.BOLD}--- {check_name} ---{Colors.END}")
        results[check_name] = check_func()
    
    # Summary
    print(f"\n{Colors.BOLD}{Colors.BLUE}=== SUMMARY ==={Colors.END}")
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for check_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        color = Colors.GREEN if result else Colors.RED
        print(f"{color}{status}{Colors.END} {check_name}")
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print(f"{Colors.GREEN}{Colors.BOLD}🎉 Environment is ready for testing!{Colors.END}")
        print("\nYou can now run:")
        print("  sudo ./run_tests_venv.sh")
        return True
    else:
        print(f"{Colors.YELLOW}{Colors.BOLD}⚠ Environment needs attention{Colors.END}")
        provide_recommendations()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}Verification interrupted{Colors.END}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.RED}Error during verification: {e}{Colors.END}")
        sys.exit(2)
