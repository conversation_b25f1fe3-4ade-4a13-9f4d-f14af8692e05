#!/usr/bin/env python3
"""
Simple test runner that works directly with your current virtual environment
This script assumes you're already in the correct venv and have <PERSON>yu installed.
"""

import os
import sys
import subprocess

def main():
    print("🚀 Simple Test Runner for Ryu Enhanced")
    print("=" * 50)
    
    # Check if we're in virtual environment
    venv = os.environ.get('VIRTUAL_ENV')
    if venv:
        print(f"✓ Virtual environment: {venv}")
    else:
        print("⚠ No virtual environment detected")
    
    # Check if running as root
    if os.geteuid() != 0:
        print("❌ This script must be run as root for Mininet access")
        print("Please run: sudo -E python3 run_tests_simple.py")
        sys.exit(1)
    
    print("✓ Running as root")
    
    # Preserve environment variables
    env = os.environ.copy()
    if venv:
        env['VIRTUAL_ENV'] = venv
        env['PATH'] = f"{venv}/bin:{env.get('PATH', '')}"
    
    # Run the main test script
    print("\n🧪 Starting comprehensive test suite...")
    print("=" * 50)
    
    try:
        # Run the test script with preserved environment
        result = subprocess.run([
            sys.executable, 'test_full_deployment.py'
        ] + sys.argv[1:], env=env)
        
        sys.exit(result.returncode)
        
    except KeyboardInterrupt:
        print("\n⚠ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error running tests: {e}")
        sys.exit(2)

if __name__ == "__main__":
    main()
