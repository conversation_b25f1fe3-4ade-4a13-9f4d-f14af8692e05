[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ryu"
version = "4.34"
description = "Component-based Software-defined Networking Framework"
readme = "README.rst"
license = {text = "Apache License 2.0"}
authors = [
    {name = "Ryu project team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Ryu project team", email = "<EMAIL>"}
]
keywords = ["openflow", "openvswitch", "openstack", "sdn", "networking"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "License :: OSI Approved :: Apache Software License",
    "Topic :: System :: Networking",
    "Natural Language :: English",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: Unix",
    "Operating System :: POSIX :: Linux",
    "Operating System :: MacOS",
    "Operating System :: Microsoft :: Windows",
]
requires-python = ">=3.8"
dependencies = [
    "eventlet>=0.40.0",
    "msgpack>=1.1.0",
    "netaddr>=1.3.0",
    "oslo.config>=10.0.0",
    "ovs>=3.5.0",
    "packaging>=25.0",
    "routes>=2.5.1",
    "tinyrpc>=1.1.0",
    "webob>=1.8.9",
]

[project.optional-dependencies]
# OF-Config support
of-config = [
    "lxml>=4.6.0",
    "ncclient>=0.6.0",
]
# NETCONF and SSH support
netconf = [
    "paramiko>=2.7.0",
    "cryptography>=3.0.0",
]
# BGP speaker SSH console
bgp-ssh = [
    "paramiko>=2.7.0",
]
# Database support for Zebra protocol
database = [
    "SQLAlchemy>=1.4.0,<2.0.0",
]
# Development dependencies
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "coverage>=6.0.0",
    "pycodestyle>=2.8.0",
    "autopep8>=1.6.0",
    "pylint>=2.12.0",
    "mypy>=1.0.0",
    "types-setuptools",
]
# Middleware support for SDN integration
middleware = [
    "pyyaml>=6.0.0",
    "requests>=2.28.0",
    "scapy>=2.5.0",
    "psutil>=5.9.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "websockets>=12.0",
]
# P4Runtime support for programmable switches
p4runtime = [
    "grpcio>=1.59.0",
    "grpcio-tools>=1.59.0",
    "protobuf>=4.25.0",
    "p4runtime>=1.4.0",
]
# All optional dependencies
all = [
    "ryu[of-config,netconf,bgp-ssh,database,middleware,p4runtime]",
]

[project.urls]
Homepage = "https://ryu-sdn.org"
Documentation = "https://ryu.readthedocs.io"
Repository = "https://github.com/faucetsdn/ryu"
"Bug Tracker" = "https://github.com/faucetsdn/ryu/issues"

[project.scripts]
ryu-manager = "ryu.cmd.manager:main"
ryu = "ryu.cmd.ryu_base:main"

[tool.setuptools.packages.find]
include = ["ryu*"]

[tool.setuptools.package-data]
"ryu" = ["etc/ryu/ryu.conf"]

[tool.pytest.ini_options]
testpaths = ["ryu/tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

[tool.coverage.run]
source = ["ryu"]
omit = [
    "*/tests/*",
    "*/test_*",
    "ryu/contrib/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "eventlet.*",
    "msgpack.*",
    "netaddr.*",
    "oslo.*",
    "ovs.*",
    "routes.*",
    "tinyrpc.*",
    "webob.*",
    "lxml.*",
    "ncclient.*",
    "paramiko.*",
    "sqlalchemy.*",
]
ignore_missing_imports = true

[tool.pycodestyle]
max-line-length = 88
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".tox",
    ".venv",
    "ryu/contrib",
]
ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
]

[tool.autopep8]
max_line_length = 88
ignore = ["E203", "E501", "W503"]
exclude = ["ryu/contrib"]

[tool.pylint.messages_control]
disable = [
    "C0103",  # invalid-name
    "C0111",  # missing-docstring
    "R0903",  # too-few-public-methods
    "R0913",  # too-many-arguments
    "W0613",  # unused-argument
]

[tool.pylint.format]
max-line-length = 88
